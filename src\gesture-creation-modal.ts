import { App, Modal, Setting, Notice } from 'obsidian';
import type { Command } from 'obsidian';
import GestureCommanderPlugin from './main.js';
import type { Point } from './gesture-recognizer.js';
import type { GestureMapping } from './settings.js';

export class GestureCreationModal extends Modal {
  plugin: GestureCommanderPlugin;
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  isDrawing = false;
  points: Point[] = [];
  gestureName = '';
  selectedCommand: Command | null = null;
  existingMapping: GestureMapping | null = null;

  constructor(app: App, plugin: GestureCommanderPlugin, existingMapping?: GestureMapping) {
    super(app);
    this.plugin = plugin;
    this.existingMapping = existingMapping || null;

    if (this.existingMapping) {
      this.gestureName = this.existingMapping.gestureName;
      this.selectedCommand = this.findCommandById(this.existingMapping.commandId);
    }
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.empty();

    contentEl.createEl('h2', {
      text: this.existingMapping ? 'Edit Gesture' : 'Create New Gesture'
    });

    this.createGestureNameInput(contentEl);
    this.createCommandSelector(contentEl);
    this.createDrawingCanvas(contentEl);
    this.createActionButtons(contentEl);

    if (this.existingMapping) {
      this.loadExistingGesture();
    }
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }

  private createGestureNameInput(containerEl: HTMLElement): void {
    new Setting(containerEl)
      .setName('Gesture name')
      .setDesc('Enter a name for this gesture')
      .addText(text => text
        .setPlaceholder('e.g., circle, triangle, arrow')
        .setValue(this.gestureName)
        .onChange((value) => {
          this.gestureName = value.trim();
        }));
  }

  private createCommandSelector(containerEl: HTMLElement): void {
    const commands = this.getAvailableCommands();

    const commandSetting = new Setting(containerEl)
      .setName('Command')
      .setDesc('Search and select the command to execute when this gesture is recognized');

    // Create search input
    const searchContainer = commandSetting.controlEl.createDiv('command-search-container');
    const searchInput = searchContainer.createEl('input', {
      type: 'text',
      placeholder: 'Type to search commands...',
      cls: 'command-search-input'
    });

    // Create results container
    const resultsContainer = searchContainer.createDiv('command-search-results');
    resultsContainer.style.display = 'none';

    // Set initial value if editing
    if (this.selectedCommand) {
      searchInput.value = this.selectedCommand.name || this.selectedCommand.id;
    }

    let filteredCommands = commands;
    let selectedIndex = -1;

    const updateResults = (query: string) => {
      resultsContainer.empty();

      if (!query.trim()) {
        resultsContainer.style.display = 'none';
        return;
      }

      // Fuzzy search
      filteredCommands = commands.filter(cmd => {
        const name = (cmd.name || cmd.id).toLowerCase();
        const id = cmd.id.toLowerCase();
        const searchTerm = query.toLowerCase();

        return name.includes(searchTerm) ||
          id.includes(searchTerm) ||
          this.fuzzyMatch(name, searchTerm) ||
          this.fuzzyMatch(id, searchTerm);
      }).slice(0, 10); // Limit to 10 results

      if (filteredCommands.length === 0) {
        resultsContainer.style.display = 'none';
        return;
      }

      selectedIndex = -1;
      resultsContainer.style.display = 'block';

      filteredCommands.forEach((cmd, index) => {
        const item = resultsContainer.createDiv('command-search-item');
        item.setAttribute('data-index', index.toString());

        const nameEl = item.createDiv('command-name');
        nameEl.textContent = cmd.name || cmd.id;

        const idEl = item.createDiv('command-id');
        idEl.textContent = cmd.id;

        item.addEventListener('click', () => {
          this.selectCommand(cmd, searchInput, resultsContainer);
        });

        item.addEventListener('mouseenter', () => {
          this.highlightItem(index, resultsContainer);
        });
      });
    };

    const selectCurrentItem = () => {
      if (selectedIndex >= 0 && selectedIndex < filteredCommands.length) {
        this.selectCommand(filteredCommands[selectedIndex], searchInput, resultsContainer);
      }
    };

    searchInput.addEventListener('input', (e) => {
      const query = (e.target as HTMLInputElement).value;
      updateResults(query);
    });

    searchInput.addEventListener('keydown', (e) => {
      if (resultsContainer.style.display === 'none') return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          selectedIndex = Math.min(selectedIndex + 1, filteredCommands.length - 1);
          this.highlightItem(selectedIndex, resultsContainer);
          break;
        case 'ArrowUp':
          e.preventDefault();
          selectedIndex = Math.max(selectedIndex - 1, -1);
          this.highlightItem(selectedIndex, resultsContainer);
          break;
        case 'Enter':
          e.preventDefault();
          selectCurrentItem();
          break;
        case 'Escape':
          e.preventDefault();
          resultsContainer.style.display = 'none';
          selectedIndex = -1;
          break;
      }
    });

    searchInput.addEventListener('focus', () => {
      if (searchInput.value.trim()) {
        updateResults(searchInput.value);
      }
    });

    // Hide results when clicking outside
    document.addEventListener('click', (e) => {
      if (!searchContainer.contains(e.target as Node)) {
        resultsContainer.style.display = 'none';
      }
    });
  }

  private fuzzyMatch(text: string, pattern: string): boolean {
    let patternIndex = 0;
    for (let i = 0; i < text.length && patternIndex < pattern.length; i++) {
      if (text[i] === pattern[patternIndex]) {
        patternIndex++;
      }
    }
    return patternIndex === pattern.length;
  }

  private highlightItem(index: number, container: HTMLElement): void {
    container.querySelectorAll('.command-search-item').forEach((item, i) => {
      item.toggleClass('selected', i === index);
    });
  }

  private selectCommand(command: Command, input: HTMLInputElement, container: HTMLElement): void {
    this.selectedCommand = command;
    input.value = command.name || command.id;
    container.style.display = 'none';
  }

  private createDrawingCanvas(containerEl: HTMLElement): void {
    const canvasContainer = containerEl.createDiv('gesture-canvas-container');
    canvasContainer.style.border = '2px solid var(--background-modifier-border)';
    canvasContainer.style.borderRadius = '8px';
    canvasContainer.style.padding = '10px';
    canvasContainer.style.marginTop = '20px';
    canvasContainer.style.marginBottom = '20px';

    canvasContainer.createEl('h4', { text: 'Draw your gesture:' });
    canvasContainer.createEl('p', {
      text: 'Click and drag to draw the gesture. The drawing will be used as a template for recognition.',
      cls: 'setting-item-description'
    });

    this.canvas = canvasContainer.createEl('canvas');
    this.canvas.width = 400;
    this.canvas.height = 300;
    this.canvas.style.border = '1px solid var(--background-modifier-border)';
    this.canvas.style.backgroundColor = 'var(--background-primary)';
    this.canvas.style.cursor = 'crosshair';
    this.canvas.style.display = 'block';
    this.canvas.style.margin = '10px auto';

    this.ctx = this.canvas.getContext('2d')!;
    this.ctx.strokeStyle = 'var(--text-accent)';
    this.ctx.lineWidth = 3;
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';

    this.setupCanvasEvents();

    // Clear button
    new Setting(canvasContainer)
      .addButton(button => button
        .setButtonText('Clear Canvas')
        .onClick(() => {
          this.clearCanvas();
        }));
  }

  private createActionButtons(containerEl: HTMLElement): void {
    const buttonContainer = containerEl.createDiv('gesture-modal-buttons');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'flex-end';
    buttonContainer.style.gap = '10px';
    buttonContainer.style.marginTop = '20px';

    // Cancel button
    const cancelButton = buttonContainer.createEl('button', { text: 'Cancel' });
    cancelButton.onclick = () => this.close();

    // Save button
    const saveButton = buttonContainer.createEl('button', {
      text: this.existingMapping ? 'Update' : 'Create',
      cls: 'mod-cta'
    });
    saveButton.onclick = () => {
      this.saveGesture().catch(error => {
        new Notice('Error saving gesture: ' + error.message);
      });
    };
  }

  private setupCanvasEvents(): void {
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.canvas.addEventListener('mouseleave', this.handleMouseUp.bind(this));

    // Touch events for mobile support
    this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this));
  }

  private handleMouseDown(event: MouseEvent): void {
    this.isDrawing = true;
    this.points = [];

    const rect = this.canvas.getBoundingClientRect();
    const point: Point = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    this.points.push(point);
    this.ctx.beginPath();
    this.ctx.moveTo(point.x, point.y);
  }

  private handleMouseMove(event: MouseEvent): void {
    if (!this.isDrawing) return;

    const rect = this.canvas.getBoundingClientRect();
    const point: Point = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    this.points.push(point);
    this.ctx.lineTo(point.x, point.y);
    this.ctx.stroke();
  }

  private handleMouseUp(): void {
    this.isDrawing = false;
  }

  private handleTouchStart(event: TouchEvent): void {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    this.handleMouseDown(mouseEvent);
  }

  private handleTouchMove(event: TouchEvent): void {
    event.preventDefault();
    const touch = event.touches[0];
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    this.handleMouseMove(mouseEvent);
  }

  private handleTouchEnd(event: TouchEvent): void {
    event.preventDefault();
    this.handleMouseUp();
  }

  private clearCanvas(): void {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.points = [];
  }

  private async saveGesture(): Promise<void> {
    if (!this.gestureName) {
      new Notice('Please enter a gesture name');
      return;
    }

    if (!this.selectedCommand) {
      new Notice('Please select a command');
      return;
    }

    if (this.points.length < 5) {
      new Notice('Please draw a gesture on the canvas');
      return;
    }

    try {
      // Remove existing templates with the same name if updating
      if (this.existingMapping) {
        this.plugin.gestureRecognizer.removeTemplatesByName(this.existingMapping.gestureName);
      }

      // Add gesture to recognizer with original canvas points
      this.plugin.gestureRecognizer.addGesture(this.gestureName, this.points);

      // Create or update mapping with original points for preview
      const mapping: GestureMapping = {
        id: this.existingMapping?.id || this.generateId(),
        gestureName: this.gestureName,
        commandId: this.selectedCommand.id,
        commandName: this.selectedCommand.name || this.selectedCommand.id,
        enabled: true,
        minScore: 0.7,
        originalPoints: [...this.points] // Store original points for preview
      };

      if (this.existingMapping) {
        // Update existing mapping
        const index = this.plugin.settings.gestureMappings.findIndex(m => m.id === this.existingMapping!.id);
        if (index !== -1) {
          this.plugin.settings.gestureMappings[index] = mapping;
        }
      } else {
        // Add new mapping
        this.plugin.settings.gestureMappings.push(mapping);
      }

      await this.plugin.saveSettings();

      new Notice(this.existingMapping ? 'Gesture updated successfully' : 'Gesture created successfully');
      this.close();
    } catch (error) {
      new Notice('Error saving gesture: ' + error.message);
    }
  }

  private loadExistingGesture(): void {
    if (!this.existingMapping) return;

    // Try to load the gesture template for drawing
    const templates = this.plugin.gestureRecognizer.getTemplatesByName(this.existingMapping.gestureName);
    if (templates.length > 0) {
      const template = templates[0];
      this.drawTemplateOnCanvas(template.points);
    }
  }

  private drawTemplateOnCanvas(points: Point[]): void {
    if (points.length === 0) return;

    this.clearCanvas();

    // Scale points to fit canvas
    const bounds = this.getBounds(points);
    const scale = Math.min(
      (this.canvas.width - 40) / bounds.width,
      (this.canvas.height - 40) / bounds.height
    );

    const offsetX = (this.canvas.width - bounds.width * scale) / 2 - bounds.minX * scale;
    const offsetY = (this.canvas.height - bounds.height * scale) / 2 - bounds.minY * scale;

    this.ctx.beginPath();
    this.ctx.moveTo(points[0].x * scale + offsetX, points[0].y * scale + offsetY);

    for (let i = 1; i < points.length; i++) {
      this.ctx.lineTo(points[i].x * scale + offsetX, points[i].y * scale + offsetY);
    }

    this.ctx.stroke();

    // Set points for editing
    this.points = points.map(p => ({
      x: p.x * scale + offsetX,
      y: p.y * scale + offsetY
    }));
  }

  private getBounds(points: Point[]): { minX: number, minY: number, width: number, height: number } {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (const point of points) {
      minX = Math.min(minX, point.x);
      minY = Math.min(minY, point.y);
      maxX = Math.max(maxX, point.x);
      maxY = Math.max(maxY, point.y);
    }

    return {
      minX,
      minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  private getAvailableCommands(): Command[] {
    return Object.values((this.app as any).commands.commands);
  }

  private findCommandById(commandId: string): Command | null {
    const commands = this.getAvailableCommands();
    return commands.find(cmd => cmd.id === commandId) || null;
  }

  private generateId(): string {
    return 'gesture-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }
}
