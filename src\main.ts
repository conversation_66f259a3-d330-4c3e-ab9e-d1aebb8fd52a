import {
  Plugin,
  Notice
} from "obsidian";
import { <PERSON><PERSON><PERSON>ognizer } from './gesture-recognizer.js';
import { GestureCapture } from './gesture-capture.js';
import type { GestureStroke } from './gesture-capture.js';
import { GestureCommanderSettingTab, DEFAULT_SETTINGS } from './settings.js';
import type { GestureCommanderSettings, GestureMapping } from './settings.js';
import { GestureCreationModal } from './gesture-creation-modal.js';

export default class GestureCommanderPlugin extends Plugin {
  settings: GestureCommanderSettings;
  gestureRecognizer: DollarRecognizer;
  gestureCapture: GestureCapture;

  async onload(): Promise<void> {
    console.log("Loading Gesture Commander plugin");
    await this.loadSettings();

    // Initialize gesture recognizer
    this.gestureRecognizer = new DollarRecognizer();

    // Initialize gesture capture
    this.initializeGestureCapture();

    // Add commands
    this.addCommand({
      id: 'gesture-commander-create-gesture',
      name: 'Create New Gesture',
      callback: () => this.openGestureCreationModal()
    });

    this.addCommand({
      id: 'gesture-commander-toggle',
      name: 'Toggle Gesture Recognition',
      callback: () => this.toggleGestureRecognition()
    });

    // Add settings tab
    this.addSettingTab(new GestureCommanderSettingTab(this.app, this));
  }

  onunload(): void {
    console.log("Unloading Gesture Commander plugin");
    if (this.gestureCapture) {
      this.gestureCapture.disable();
    }
  }

  private initializeGestureCapture(): void {
    const captureSettings = {
      modifierKeys: this.settings.modifierKeys,
      minStrokeLength: this.settings.minStrokeLength,
      maxStrokeTime: this.settings.maxStrokeTime,
      enableVisualFeedback: this.settings.enableVisualFeedback
    };

    this.gestureCapture = new GestureCapture(
      captureSettings,
      (stroke: GestureStroke) => this.handleGestureComplete(stroke)
    );

    this.gestureCapture.enable();
  }

  private handleGestureComplete(stroke: GestureStroke): void {
    const result = this.gestureRecognizer.recognize(stroke.points, this.settings.useProtractor);

    if (result.score >= this.settings.recognitionThreshold) {
      const mapping = this.settings.gestureMappings.find(
        m => m.enabled && m.gestureName === result.name && result.score >= m.minScore
      );

      if (mapping) {
        this.executeCommand(mapping.commandId);
        new Notice(`Gesture "${result.name}" recognized (${(result.score * 100).toFixed(1)}%)`);
      } else {
        new Notice(`Gesture "${result.name}" recognized but no command mapped`);
      }
    } else {
      new Notice(`Gesture not recognized (score: ${(result.score * 100).toFixed(1)}%)`);
    }
  }

  private executeCommand(commandId: string): void {
    const command = (this.app as any).commands.commands[commandId];
    if (command) {
      (this.app as any).commands.executeCommandById(commandId);
    } else {
      new Notice(`Command "${commandId}" not found`);
    }
  }

  openGestureCreationModal(existingMapping?: GestureMapping): void {
    new GestureCreationModal(this.app, this, existingMapping).open();
  }

  openGestureEditModal(mapping: GestureMapping): void {
    this.openGestureCreationModal(mapping);
  }

  private toggleGestureRecognition(): void {
    if (this.gestureCapture) {
      // For now, just show a notice. Could implement enable/disable functionality
      new Notice("Gesture recognition is active. Configure in settings to modify behavior.");
    }
  }

  updateGestureCapture(): void {
    if (this.gestureCapture) {
      this.gestureCapture.updateSettings({
        modifierKeys: this.settings.modifierKeys,
        minStrokeLength: this.settings.minStrokeLength,
        maxStrokeTime: this.settings.maxStrokeTime,
        enableVisualFeedback: this.settings.enableVisualFeedback
      });
    }
  }

  async loadSettings(): Promise<void> {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }

  async saveSettings(): Promise<void> {
    await this.saveData(this.settings);
  }
}
